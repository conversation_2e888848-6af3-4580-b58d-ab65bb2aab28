{
  // https://nuxt.com/docs/guide/concepts/typescript
  "extends": "./tsconfig.base.json",
  // "compileOnSave": false,
  "files": [],
  "references": [
    {
      "path": "./.nuxt/tsconfig.app.json"
    },
    {
      "path": "./.nuxt/tsconfig.server.json"
    },
    {
      "path": "./.nuxt/tsconfig.shared.json"
    },
    {
      "path": "./.nuxt/tsconfig.node.json"
    }
  ],
  "exclude": [
    "node_modules",
    "dist",
    ".output",
    ".nuxt"
  ]
}
