// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs';
import eslint from '@eslint/js';
import tseslint from 'typescript-eslint';
import stylistic from '@stylistic/eslint-plugin';
import prettier from 'eslint-plugin-prettier/recommended';
import globals from 'globals';
import vue from 'eslint-plugin-vue';
import vueParser from 'vue-eslint-parser';
// import oxlint from  'eslint-plugin-oxlint';

export default withNuxt(
  {
    ignores: [
      '**/dist',
      '.nuxt/**',
      '.output/**',
      // Temporary compiled files
      // '**/.*',
      // '**/._*',
      // JS files at the root of the project
      '*.js',
      '*.cjs',
      '*.mjs',
    ],
    // extends: ['@nuxt/eslint-config', 'prettier'],
  },
  eslint.configs.recommended,
  // tseslint.configs.eslintRecommended,
  ...tseslint.configs.recommended,
  stylistic.configs.recommended,
  {
    languageOptions: {
      parserOptions: {
        warnOnUnsupportedTypeScriptVersion: false,
        sourceType: 'module',
        ecmaVersion: 'latest',
      },
    },
  },
  {
    files: ['**/*.vue'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: await import('@typescript-eslint/parser'),
        sourceType: 'module',
        ecmaVersion: 'latest',
      },
    },
  },
  ...vue.configs['flat/recommended'],
  /* {
    files: [
      '**!/!*.ts',
      '**!/!*.tsx',
      '**!/!*.cts',
      '**!/!*.mts',
      '**!/!*.js',
      '**!/!*.jsx',
      '**!/!*.cjs',
      '**!/!*.mjs',
    ],
    // Override or add rules here
    rules: {},
  }, */
  {
    files: ['**/*.config.js', '**/*.config.cjs', '**/*.config.mjs', '**/*.config.ts', '**/*.config.cts', '**/*.config.mts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
  // ...oxlint.configs['flat/recommended'],
  {
    rules: {
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': ['warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
      'import/newline-after-import': [
        'error',
        {
          count: 1,
          exactCount: true,
          considerComments: true,
        },
      ],
      // Stylistic
      'max-len': 'off',
      '@stylistic/max-len': ['warn', { code: 180, tabWidth: 2, ignoreComments: true }],
      // semi: ['error', 'always'],
      'sort-keys': 'off',
      semi: 'off',
      '@stylistic/semi': ['error', 'always'],
      quotes: 'off',
      '@stylistic/quote-props': ['error', 'as-needed'],
      '@stylistic/arrow-parens': ['error', 'always'],
      'member-delimiter-style': 'off',
      '@stylistic/member-delimiter-style': [
        'error',
        {
          multiline: {
            delimiter: 'comma',
            requireLast: true,
          },
          singleline: {
            delimiter: 'comma',
            requireLast: false,
          },
          overrides: {
            interface: {
              multiline: {
                delimiter: 'semi',
                requireLast: true,
              },
              singleline: {
                delimiter: 'semi',
                requireLast: false,
              },
            },
            typeLiteral: {
              multiline: {
                delimiter: 'semi',
                requireLast: true,
              },
              singleline: {
                delimiter: 'semi',
                requireLast: false,
              },
            },
          },
        },
      ],
      // Typescript
      '@typescript-eslint/no-empty-object-type': [
        'warn',
        {
          allowInterfaces: 'with-single-extends',
          allowObjectTypes: 'never',
          allowWithName: 'Props$',
        },
      ],
      '@typescript-eslint/no-unsafe-function-type': ['warn'],
      '@typescript-eslint/no-wrapper-object-types': ['warn'],
      // '@typescript-eslint/no-explicit-any': ['warn'],
      '@typescript-eslint/no-explicit-any': [
        'warn', // error 대신 warn 사용
        {
          ignoreRestArgs: true,
          fixToUnknown: false,
        },
      ],
      // Vue
      'vue/multi-word-component-names': 'off',
      /* 'vue/multi-word-component-names': [
        'warn',
        {
          ignores: ['App', 'Index'],
        },
      ], */
      'vue/singleline-html-element-content-newline': 'off',
      /* 'vue/singleline-html-element-content-newline': [
        'error',
        {
          ignoreWhenNoAttributes: true,
          ignoreWhenEmpty: true,
          ignores: ['pre', 'textarea', 'span', 'button', 'label', 'a', 'code', 'small'],
        },
      ], */
      'vue/max-attributes-per-line': 'off',
      'vue/html-self-closing': 'off',
    },
    languageOptions: {
      sourceType: 'module',
      globals: {
        ...globals.browser,
      },
    },
  },
  prettier, // as ConfigArray[number],
);

