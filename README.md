# Next Samsung+ Web

Look at [Nuxt docs](https://nuxt.com/docs/getting-started/introduction) and [Nuxt UI docs](https://ui.nuxt.com) to learn more.

## Setup

For IDE for development

### IntelliJ IDEA or WebStorm

Install plugins: Oxc, Biome, i18n Ally,

### Visual Source Code

Install extensions: Vue (Official), Vitest, ESLint, Prettier - Code formatter, Oxc, Biome, Tailwind CSS IntelliSense, i18n Ally

Add below to .vscode/settings.json
```json
{
  "files.associations": {
    "*.css": "tailwindcss",
    "*.scss": "tailwindcss"
  },
  "editor.quickSuggestions": {
    "strings": "on"
  },
  "tailwindCSS.classAttributes": ["class", "ui"],
  "tailwindCSS.experimental.classRegex": [
    ["ui:\\s*{([^)]*)\\s*}", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

Make sure to install the dependencies:

### Node.js >= 22.18.0

NVM is recommended

```bash
nvm install 22.18.0 # 혹은, nvm install --lts
nvm use default 22.18.0 # 혹은, nvm use default --lts
nvm use 22.18.0
```

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install
```

## Development Server

Start the development server on `http://localhost:4300`:

```bash
# npm
npm run dev

# pnpm
pnpm run dev

# yarn
yarn dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm run build

# yarn
yarn build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm run preview

# yarn
yarn preview
```

Lint:

```bash
# npm
npm lint

# pnpm
pnpm lint
```

Lint with fix:

```bash
# npm
npm lint:fix

# pnpm
pnpm lint:fix
```

Test typecheck:

```bash
# npm
npm test:typecheck

# pnpm
pnpm test:typecheck
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## Naming Rules

### Component

PascalCase

- "<PascalCase>Component</PascalCase>": "components/PascalCase.vue"
- "<PascalCase>Page</PascalCase>": "page/PascalCase.vue"

### Composable

camelCase + use prefix

- "const { ... } = useCamelCase()": "composables/useCamelCase.ts"
- "const { ... } = useCamelCase()": "stores/useCamelCase.ts"
- export pattern

  ```ts
  export const useCamelCase = () => {
    ...
  }
  ```

## Trouble Shooting

base

-
