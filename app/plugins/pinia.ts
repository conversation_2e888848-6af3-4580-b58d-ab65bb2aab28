import type { Pinia } from 'pinia';
import { defineNuxtPlugin } from 'nuxt/app';
import { createPersistedState } from 'pinia-plugin-persistedstate';
import { useMainStore } from '~/stores';

export default defineNuxtPlugin({
  name: 'pinia-store',
  dependsOn: ['pinia'],
  setup: async (nuxtApp) => {
    const $pinia = nuxtApp.$pinia as unknown as Pinia;
    $pinia.use(createPersistedState());
    return {
      provide: {
        store: useMainStore($pinia),
      },
    };
  },
});
