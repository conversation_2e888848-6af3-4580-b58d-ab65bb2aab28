import { defineNuxtPlugin } from 'nuxt/app';

export default defineNuxtPlugin((nuxtApp) => {
  // called right before setting a new locale
  nuxtApp.hook('$i18n:beforeLocaleSwitch', (oldLocale, newLocale, initialSetup, _nuxtApp) => {
    console.log('onBeforeLanguageSwitch', oldLocale, newLocale, initialSetup);

    // You can override the new locale by setting it to a different value
    if (switchData.newLocale === 'fr') {
      switchData.newLocale = 'en';
    }
  });

  // called right after a new locale has been set
  nuxtApp.hook('i18n:localeSwitched', (oldLocale, newLocale) => {
    console.log('onLanguageSwitched', oldLocale, newLocale);
  });
});
