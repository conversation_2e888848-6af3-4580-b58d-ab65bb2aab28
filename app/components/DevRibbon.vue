<script setup lang="ts">
const props = defineProps<{
  src?: string;
}>();

// 개발 환경에서만 표시 (디버깅을 위해 임시로 true로 설정)
const isDev = ref(true); // 임시로 true, 나중에 process.env.NODE_ENV === 'development'로 변경

// 플러그인에서 관리하는 현재 파일 정보 사용
const currentFile = useState('dev-current-file', () => props.src);

const currentFileName = computed(() => {
  return currentFile.value || 'unknown.vue';
});

// 클릭 시 콘솔에 파일 경로 출력
const handleClick = () => {
  console.log(`📁 Current file: ${currentFileName.value}`);
  console.log(`🌐 Current route: ${useRoute().path}`);
};

// 컴포넌트 마운트 확인
onMounted(() => {
  console.log('🎯 DevRibbon mounted!', {
    isDev: isDev.value,
    currentFileName: currentFileName.value,
    nodeEnv: process.env.NODE_ENV,
    // element: document.querySelector('.dev-file-ribbon')
  });
});
</script>

<template>
  <div v-if="isDev" class="dev-ribbon" @click="handleClick">
    {{ currentFileName }}
  </div>
</template>

<style lang="scss">
.dev-ribbon {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  background: #ff6b6b !important;
  color: white !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-family: monospace !important;
  font-weight: bold !important;
  border-bottom-left-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  cursor: pointer !important;
  user-select: none !important;
  min-width: 100px !important;
  text-align: center !important;
}

.dev-ribbon:hover {
  transform: translateX(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 다크 모드 지원 */
@media (prefers-color-scheme: dark) {
  .dev-ribbon {
    background: linear-gradient(135deg, #4c1d95 0%, #1e1b4b 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}

/* 모바일에서는 작게 표시 */
@media (max-width: 768px) {
  .dev-ribbon {
    font-size: 10px !important;
    padding: 6px 12px !important;
  }
}
</style>
