<script setup lang="ts">
import { useQueryClient, useQuery, useMutation } from '@tanstack/vue-query';
import PostsList from './posts-list.vue';
import type { Post } from './types';

// Access QueryClient instance
const queryClient = useQueryClient();

const postId = ref<number | null>(null);

const getTodos = async () => {
  return fetch('https://jsonplaceholder.typicode.com/todos').then((res) => res.json());
};

const postTodo = async (newTodo: Post) => {
  const res = await fetch('https://jsonplaceholder.typicode.com/todos', {
    method: 'POST',
    body: JSON.stringify(newTodo),
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return await res.json();
};

// Query
const { isPending, isError, data, error } = useQuery({
  queryKey: ['todos'],
  queryFn: getTodos,
});

// Mutation
const mutation = useMutation({
  mutationFn: postTodo,
  onSuccess: () => {
    // Invalidate and refetch
    queryClient.invalidateQueries({ queryKey: ['todos'] });
  },
});

function onButtonClick() {
  mutation.mutate({
    id: Date.now(),
    title: 'Do Laundry',
  });
}

function onIsVisited(id: number) {
  postId.value = id;
  return true;
}
</script>

<template>
  <div>
    <PostsList :is-visited="onIsVisited" />
    <span v-if="isPending">Loading...</span>
    <span v-else-if="isError">Error: {{ error.message }}</span>
    <!-- We can assume by this point that `isSuccess === true` -->
    <ul v-else class="border border-solid border-b-amber-100">
      <li v-for="todo in data" :key="todo.id">
        {{ todo.title }}
      </li>
    </ul>
    <UButton @click="onButtonClick"> Add Todo </UButton>
  </div>
</template>
