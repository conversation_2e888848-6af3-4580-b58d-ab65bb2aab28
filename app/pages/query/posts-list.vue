<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import type { Post } from './types';

const props = defineProps<{
  isVisited: (id: number) => boolean;
}>();
const emit = defineEmits<{
  setPostId: [(id: number) => void];
}>();

const fetcher = async (): Promise<Post[]> => await fetch('https://jsonplaceholder.typicode.com/posts').then((response) => response.json());

const { isPending, isError, isFetching, data, error, refetch } = useQuery({
  queryKey: ['posts'],
  queryFn: fetcher,
});
</script>

<template>
  <div>
    <h1>Posts</h1>
    <div v-if="isPending">Loading...</div>
    <div v-else-if="isError">An error has occurred: {{ error }}</div>
    <div v-else-if="data">
      <ul>
        <li v-for="item in data" :key="item.id">
          <a href="#" :class="{ visited: props.isVisited(item.id) }" @click="$emit('setPostId', (item as Post).id)">{{ item.title }}</a>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.visited {
  font-weight: bold;
  color: green;
}
</style>
