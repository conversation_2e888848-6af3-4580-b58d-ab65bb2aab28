<script setup lang="ts">
definePageMeta({
  layout: 'mobile-default',
});

// const localePath = useLocalePath();
</script>

<template>
  <div class="page flex h-screen flex-col items-center justify-center gap-4">
    <h1 class="text-2xl font-bold text-(--ui-primary)">About <ColorModeButton /></h1>

    <div class="bg-accented flex items-center gap-2">
      <!-- <NuxtLinkLocale to="/">{{ $t('home') }}</NuxtLinkLocale> -->
      <NuxtLink to="/">{{ $t('home') }}</NuxtLink>
    </div>
  </div>
</template>

<style>
@media (min-width: 768px) {
  .page {
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
  }
}
</style>
