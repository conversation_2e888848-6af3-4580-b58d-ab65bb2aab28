<script setup lang="ts">
const locales = useLocales();
const locale = useLocale();
const date = useLocaleDate(new Date('2025-08-15'));
</script>

<template>
  <div>
    <h1>Nuxt birthday</h1>
    <p>{{ date }}</p>
    <label for="locale-chooser">Preview a different locale</label>
    <select id="locale-chooser" v-model="locale">
      <option v-for="localeItem of locales" :key="localeItem" :value="localeItem">
        {{ localeItem }}
      </option>
    </select>
  </div>
</template>
