<script setup lang="ts">
// ✅ 사용자 정보 가져오기 (SSR/CSR 둘 다 동작)
const {
  data: user,
  refresh,
  pending,
  error,
} = await useAsyncData(
  'user',
  () => $fetch('/api/user'), // 서버 API 호출
);

// ✅ 사용자 이름 변경
const newName = ref('');

const updateUser = async () => {
  try {
    await $fetch('/api/user', {
      method: 'POST',
      body: { name: newName.value },
    });
    // 변경 후 다시 불러오기
    await refresh();
    newName.value = '';
  } catch (err) {
    console.error('업데이트 실패:', err);
  }
};
</script>

<template>
  <div class="p-6">
    <h1 class="mb-4 text-xl font-bold">내 프로필</h1>

    <!-- 로딩 상태 -->
    <div v-if="pending">불러오는 중...</div>
    <!-- 에러 -->
    <div v-else-if="error" class="text-red-500">에러 발생: {{ error.message }}</div>
    <!-- 사용자 정보 -->
    <div v-else>
      <p><strong>ID:</strong> {{ user.id }}</p>
      <p><strong>이름:</strong> {{ user.name }}</p>
    </div>

    <!-- 사용자 정보 수정 -->
    <div class="mt-6">
      <input v-model="newName" type="text" placeholder="새 이름 입력" class="mr-2 rounded border p-2" />
      <button class="rounded bg-blue-500 px-4 py-2 text-white" @click="updateUser">이름 변경</button>
    </div>
  </div>
</template>
