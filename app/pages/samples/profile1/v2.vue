<script setup lang="ts">
// 타입 정의
interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  birthDate?: string;
  bio?: string;
  interests: string[];
  notifications: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FormData {
  name: string;
  email: string;
  phone: string;
  birthDate: string;
  bio: string;
  interests: string[];
  notifications: boolean;
}

interface Message {
  type: 'success' | 'error';
  text: string;
}

// 메타 정보 설정
useSeoMeta({
  title: '사용자 프로필',
  description: '사용자 정보를 확인하고 수정할 수 있습니다.',
});

// 페이지 설정
definePageMeta({
  title: '프로필 관리',
  layout: 'default',
});

// 반응형 데이터
const isUpdating = ref(false);
const message = ref<Message | null>(null);
const availableInterests = ['기술', '디자인', '사진', '음악', '여행', '독서', '운동', '요리'];

// 초기 데이터 페칭 (SSR/CSR 모두 지원)
const {
  data: profile,
  pending,
  error,
  refresh,
} = await useFetch<UserProfile>('/api/profile', {
  key: 'user-profile',
  server: true, // SSR에서도 실행
  default: () => ({
    id: '',
    name: '',
    email: '',
    phone: '',
    birthDate: '',
    bio: '',
    interests: [],
    notifications: false,
    createdAt: '',
    updatedAt: '',
  }),
});

// 폼 데이터 초기화 및 관리
const formData = ref<FormData>({
  name: '',
  email: '',
  phone: '',
  birthDate: '',
  bio: '',
  interests: [],
  notifications: false,
});

const originalData = ref<FormData>({
  name: '',
  email: '',
  phone: '',
  birthDate: '',
  bio: '',
  interests: [],
  notifications: false,
});

// 프로필 데이터가 로드되면 폼 데이터 초기화
watch(
  profile,
  (newProfile) => {
    if (newProfile) {
      const formValues: FormData = {
        name: newProfile.name || '',
        email: newProfile.email || '',
        phone: newProfile.phone || '',
        birthDate: newProfile.birthDate || '',
        bio: newProfile.bio || '',
        interests: [...(newProfile.interests || [])],
        notifications: newProfile.notifications || false,
      };

      formData.value = { ...formValues };
      originalData.value = { ...formValues };
    }
  },
  { immediate: true },
);

// 변경사항 체크
const hasChanges = computed(() => {
  return JSON.stringify(formData.value) !== JSON.stringify(originalData.value);
});

// 폼 제출 처리
const handleSubmit = async () => {
  if (!hasChanges.value) return;

  isUpdating.value = true;
  message.value = null;

  try {
    // API 요청
    const updatedProfile = await $fetch<UserProfile>('/api/profile', {
      method: 'PUT',
      body: formData.value,
    });

    // 성공시 원본 데이터 업데이트
    if (updatedProfile) {
      originalData.value = { ...formData.value };

      // 캐시된 데이터도 업데이트
      await refresh();

      message.value = {
        type: 'success',
        text: '프로필이 성공적으로 업데이트되었습니다.',
      };

      // 3초 후 메시지 제거
      setTimeout(() => {
        message.value = null;
      }, 3000);
    }
  } catch (err) {
    console.error('프로필 업데이트 실패:', err);
    message.value = {
      type: 'error',
      text: '프로필 업데이트에 실패했습니다. 다시 시도해주세요.',
    };
  } finally {
    isUpdating.value = false;
  }
};

// 폼 초기화
const resetForm = () => {
  formData.value = { ...originalData.value };
  message.value = null;
};

// 페이지 이탈시 변경사항 확인
onBeforeRouteLeave((to, from, next) => {
  if (hasChanges.value && !isUpdating.value) {
    const answer = window.confirm('저장하지 않은 변경사항이 있습니다. 정말로 페이지를 떠나시겠습니까?');
    next(answer);
  } else {
    next();
  }
});

// 브라우저 새로고침/닫기시 변경사항 확인
onMounted(() => {
  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    if (hasChanges.value && !isUpdating.value) {
      e.preventDefault();
      e.returnValue = '';
    }
  };

  window.addEventListener('beforeunload', handleBeforeUnload);

  onUnmounted(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="mx-auto max-w-2xl px-4">
      <!-- 헤더 -->
      <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
        <h1 class="text-2xl font-bold text-gray-900">사용자 프로필</h1>
        <p class="mt-1 text-gray-600">계정 정보를 확인하고 수정할 수 있습니다.</p>
      </div>

      <!-- 로딩 상태 -->
      <div v-if="pending" class="rounded-lg bg-white p-6 shadow-sm">
        <div class="animate-pulse">
          <div class="mb-4 h-4 rounded bg-gray-200" />
          <div class="mb-4 h-4 rounded bg-gray-200" />
          <div class="mb-4 h-4 rounded bg-gray-200" />
        </div>
      </div>

      <!-- 에러 상태 -->
      <div v-else-if="error" class="rounded-lg bg-white p-6 shadow-sm">
        <div class="py-8 text-center">
          <div class="mb-4 text-red-500">
            <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 class="mb-2 text-lg font-semibold text-gray-900">데이터를 불러올 수 없습니다</h3>
          <p class="mb-4 text-gray-600">{{ error }}</p>
          <button class="rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600" @click="refresh()">다시 시도</button>
        </div>
      </div>

      <!-- 프로필 폼 -->
      <div v-else class="rounded-lg bg-white p-6 shadow-sm">
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <!-- 기본 정보 섹션 -->
          <div class="border-b border-gray-200 pb-6">
            <h2 class="mb-4 text-lg font-semibold text-gray-900">기본 정보</h2>

            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label for="name" class="mb-2 block text-sm font-medium text-gray-700"> 이름 * </label>
                <input
                  id="name"
                  v-model="formData.name"
                  type="text"
                  required
                  :disabled="isUpdating"
                  class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100"
                  placeholder="이름을 입력하세요"
                />
              </div>

              <div>
                <label for="email" class="mb-2 block text-sm font-medium text-gray-700"> 이메일 * </label>
                <input
                  id="email"
                  v-model="formData.email"
                  type="email"
                  required
                  :disabled="isUpdating"
                  class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label for="phone" class="mb-2 block text-sm font-medium text-gray-700"> 전화번호 </label>
                <input
                  id="phone"
                  v-model="formData.phone"
                  type="tel"
                  :disabled="isUpdating"
                  class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100"
                  placeholder="010-0000-0000"
                />
              </div>

              <div>
                <label for="birthDate" class="mb-2 block text-sm font-medium text-gray-700"> 생년월일 </label>
                <input
                  id="birthDate"
                  v-model="formData.birthDate"
                  type="date"
                  :disabled="isUpdating"
                  class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100"
                />
              </div>
            </div>
          </div>

          <!-- 선택사항 섹션 -->
          <div class="pb-6">
            <h2 class="mb-4 text-lg font-semibold text-gray-900">추가 정보</h2>

            <div class="space-y-4">
              <div>
                <label for="bio" class="mb-2 block text-sm font-medium text-gray-700"> 자기소개 </label>
                <textarea
                  id="bio"
                  v-model="formData.bio"
                  rows="4"
                  :disabled="isUpdating"
                  class="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100"
                  placeholder="자신을 간단히 소개해주세요..."
                />
              </div>

              <div>
                <label class="mb-3 block text-sm font-medium text-gray-700"> 관심사 </label>
                <div class="flex flex-wrap gap-2">
                  <label v-for="interest in availableInterests" :key="interest" class="inline-flex items-center">
                    <input
                      v-model="formData.interests"
                      :value="interest"
                      type="checkbox"
                      :disabled="isUpdating"
                      class="focus:ring-opacity-50 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 disabled:cursor-not-allowed"
                    />
                    <span class="ml-2 text-sm text-gray-700">{{ interest }}</span>
                  </label>
                </div>
              </div>

              <div>
                <label class="flex items-center">
                  <input
                    v-model="formData.notifications"
                    type="checkbox"
                    :disabled="isUpdating"
                    class="focus:ring-opacity-50 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 disabled:cursor-not-allowed"
                  />
                  <span class="ml-2 text-sm text-gray-700">이메일 알림 받기</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 액션 버튼 -->
          <div class="flex justify-between border-t border-gray-200 pt-6">
            <button
              type="button"
              :disabled="isUpdating"
              class="rounded-lg bg-gray-100 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50"
              @click="resetForm"
            >
              초기화
            </button>

            <div class="flex space-x-3">
              <button
                type="button"
                :disabled="isUpdating"
                class="rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                @click="navigateTo('/')"
              >
                취소
              </button>

              <button
                type="submit"
                :disabled="isUpdating || !hasChanges"
                class="flex items-center rounded-lg bg-blue-500 px-6 py-2 text-white transition-colors hover:bg-blue-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <svg v-if="isUpdating" class="mr-2 -ml-1 h-4 w-4 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                {{ isUpdating ? '저장 중...' : '저장' }}
              </button>
            </div>
          </div>
        </form>

        <!-- 성공/에러 메시지 -->
        <div v-if="message" class="mt-4">
          <div
            :class="['rounded-lg p-3 text-sm', message.type === 'success' ? 'border border-green-200 bg-green-50 text-green-700' : 'border border-red-200 bg-red-50 text-red-700']"
          >
            {{ message.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
