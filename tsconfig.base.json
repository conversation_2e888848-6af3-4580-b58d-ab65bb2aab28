{
  "compilerOptions": {
    "forceConsistentCasingInFileNames": true,
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": true,
    "importHelpers": true,
    "isolatedModules": true,
    "lib": ["ES2022"],
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "noEmitOnError": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "ES2022",
    "customConditions": ["development"],
    "baseUrl": ".",
    "paths": {
      "#imports": ["./.nuxt/imports.d.ts"],
      "images": ["./assets/images"],
      "style": ["./assets/style"],
      "data": ["./assets/other/data'"],
      "stores": ["./stores"],
      "utils": ["./utils"],
    }
  }
}
