// sample
import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, readBody, createError } from '#imports';

// 사용자 프로필 타입 정의
interface UserProfile {
  id?: string;
  name: string;
  email: string;
  phone: string;
  birthDate: string;
  bio: string;
  interests: string[];
  notifications: boolean;
  createdAt: string;
  updatedAt: string;
}

const mockupData = {
  name: '홍길동',
  email: '<EMAIL>',
  phone: '010-1234-5678',
  birthDate: '1990-01-15',
  bio: '안녕하세요! 웹 개발을 좋아하는 개발자입니다.',
  interests: ['기술', '디자인', '음악'],
  notifications: true,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-12-15T10:30:00.000Z',
} as UserProfile;

// 사용자 프로필 조회 (실제로는 DB에서 가져옴)
async function getUserProfile(userId: string) {
  // 시뮬레이션을 위한 지연
  await new Promise((resolve) => setTimeout(resolve, 500));

  mockupData.id = userId;

  // 실제 구현에서는 데이터베이스에서 조회
  return mockupData;
}

// 사용자 프로필 업데이트 (실제로는 DB에 저장)
async function updateUserProfile(userId: string, updateData: UserProfile) {
  // 유효성 검사
  if (!updateData.name || !updateData.email) {
    throw createError({
      statusCode: 400,
      statusMessage: '이름과 이메일은 필수 항목입니다.',
    });
  }

  // 이메일 형식 검사
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(updateData.email)) {
    throw createError({
      statusCode: 400,
      statusMessage: '올바른 이메일 형식이 아닙니다.',
    });
  }

  // 시뮬레이션을 위한 지연
  await new Promise((resolve) => setTimeout(resolve, 800));

  // 실제 구현에서는 데이터베이스 업데이트
  const updatedProfile = {
    id: userId,
    ...updateData,
    updatedAt: new Date().toISOString(),
  };

  return updatedProfile;
}

export default defineEventHandler(async (event) => {
  const method = getMethod(event);

  // 인증 체크 (실제 구현에서는 JWT 토큰 등을 확인)
  const userId = getCookie(event, 'user-id') || 'demo-user-123';

  if (!userId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  if (method === 'GET') {
    return await getUserProfile(userId);
  } else if (method === 'PUT') {
    const body = await readBody(event);
    return await updateUserProfile(userId, body);
  } else {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method Not Allowed',
    });
  }
});
