{"name": "@nextssp/web/remote", "version": "0.0.1", "private": true, "type": "module", "description": "", "author": "keiches <<EMAIL>>", "license": "PRIVATE", "keywords": ["samsung", "vue", "nuxt"], "scripts": {"build": "NITRO_PORT=3001 nuxt build", "build:debug": "DEBUG=vite:* npm run build", "build:resolve-ids": "SAVE_RESOLVE_IDS=true nuxt build", "dev": "nuxt dev --verbose", "dev:debug": "DEBUG=vite:* nuxt dev --verbose", "generate": "nuxt generate", "preview": "NITRO_PORT=3001 nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint --fix .", "test:typecheck": "TYPECHECK=true nuxt prepare && TYPECHECK=true nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.62", "@iconify-json/simple-icons": "^1.2.47", "@nuxt/content": "^3.6.3", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^2.0.0", "@nuxt/image": "^1.11.0", "@nuxt/scripts": "^0.11.10", "@nuxt/test-utils": "^3.19.2", "@nuxt/ui": "^3.3.2", "@nuxtjs/i18n": "^10.0.5", "@peterbud/nuxt-query": "^1.1.0", "@pinia/nuxt": "^0.11.2", "@tanstack/vue-query": "^5.85.3", "@tanstack/vue-table": "^8.21.3", "@unhead/vue": "^2.0.14", "@vueuse/nuxt": "^13.7.0", "eslint-plugin-oxlint": "^1.12.0", "nuxt": "^4.0.3", "pinia": "^3.0.3", "vue": "^3.5.18"}, "devDependencies": {"@eslint/js": "^9.33.0", "@module-federation/runtime-core": "^0.18.3", "@module-federation/sdk": "^0.18.3", "@module-federation/vite": "^1.7.1", "@nuxt/eslint": "^1.8.0", "@originjs/vite-plugin-federation": "^1.4.1", "@stylistic/eslint-plugin": "^5.2.3", "@typescript-eslint/parser": "^8.40.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "globals": "^16.3.0", "pinia-plugin-persistedstate": "^4.5.0", "prettier": "^3.6.2", "prettier-plugin-jsdoc": "^1.3.3", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1", "unplugin-vue-components": "^29.0.0", "vite-plugin-eslint2": "^5.0.4", "vite-plugin-oxlint": "^1.4.0", "vite-plugin-stylelint": "^6.0.2", "vite-plugin-top-level-await": "^1.6.0", "vue-eslint-parser": "^10.2.0", "vue-gtag-next": "^1.14.0"}, "lint-staged": {"**/*.{md,json}": ["eslint --cache --fix"], "**/*.{js,cjs,mjs,jsx,ts,tsx,mts,cts,vue}": ["ox<PERSON>"]}}