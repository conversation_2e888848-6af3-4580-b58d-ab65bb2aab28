// import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { defineNuxtConfig } from 'nuxt/config';

// import { defineContentConfig, defineCollection } from '@nuxt/content';

// import federation from '@originjs/vite-plugin-federation';
import { federation } from '@module-federation/vite';
import topLevelAwait from 'vite-plugin-top-level-await';

// import oxlint from 'vite-plugin-oxlint';
import eslint from 'vite-plugin-eslint2';
import stylelint from 'vite-plugin-stylelint';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    '@nuxt/ui',
    '@nuxt/eslint',
    '@nuxt/content',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/test-utils',
    '@nuxt/scripts',
    '@nuxtjs/i18n',
    '@pinia/nuxt',
    '@peterbud/nuxt-query',
    '@vueuse/nuxt',
  ],

  $development: {
    //
  },

  $env: {
    staging: {
      //
    },
  },

  $production: {
    routeRules: {
      '/**': { isr: true },
    },
  },

  ssr: true,

  components: true,

  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },

  app: {
    head: {
      /* NOTE: 외부 static css 등을 추가하고 싶을 때,
      link: [{ rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css' }]
      */
    },
  },

  css: ['~/assets/css/main.css'],

  ui: {
    // prefix: 'U',
    // colorMode: false,
    // fonts: false,
    theme: {
      colors: ['primary', 'secondary', 'tertiary', 'info', 'success', 'warning', 'error'],
    },
  },

  alias: {
    images: fileURLToPath(new URL('./assets/images', import.meta.url)),
    style: fileURLToPath(new URL('./assets/style', import.meta.url)),
    data: fileURLToPath(new URL('./assets/other/data', import.meta.url)),
    store: fileURLToPath(new URL('./stores', import.meta.url)),
    utils: fileURLToPath(new URL('./utils', import.meta.url)),
  },

  sourcemap: true,
  /* sourcemap: {
    'server': true,
    'client': false
  }, */

  devServer: {
    host: 'localhost',
    port: 4300,
  },

  future: {
    compatibilityVersion: 4,
  },

  // when enabling ssr options, you need to disable inlineStyles and maybe devLogs
  features: {
    devLogs: false,
    inlineStyles: false,
  },

  experimental: {
    asyncContext: true,
    debugModuleMutation: false,
  },

  compatibilityDate: '2025-07-16',

  nitro: {
    // preset: 'node-server', // SSR 서버 빌드
    minify: false,
    // inlineDynamicImports: true,
    /* externals: {
      inline: ['@module-federation/runtime-core', '@module-federation/error-codes'],
    }, */
    externals: {
      // Mark these virtual modules as external for Rollup
      inline: ['virtual:mf-REMOTE_ENTRY_ID'],
    },
  },

  vite: {
    logLevel: 'info', // 'silent' | 'error' | 'warn' | 'info'
    plugins: [
      // oxlint(),
      eslint(),
      stylelint(),
      federation({
        name: 'remote',
        filename: 'remoteEntry.js',
        exposes: {
          // './app': '~/app.vue',
          DevRibbon: '~/components/DevRibbon.vue',
          // Pinia
          // './stores': './stores',
        },
        runtimePlugins: ['./federation/plugins/runtime'],
        /* remotes: {
          remote: {
            type: 'module',
            name: 'host',
            entry: 'http://[...]/remoteEntry.js',
            entryGlobalName: 'host',
            shareScope: 'default',
          },
          // '@web-host': 'http://localhost:4200/_nuxt/mf-manifest.json',
          // '@namespace/viteViteRemote': 'viteRemote@http://localhost:3000/_nuxt/mf-manifest.json',
        }, */
        shared: ['vue'],
      }),
      /* federation({
        name: 'lms',
        remotes: {
          host: 'host@http://localhost:4200/_nuxt/mf-manifest.json',
        },
        filename: 'remoteEntry.js',
        // runtimePlugins: ['./federation/plugins/runtime'],
        exposes: {
          // './app': './app.vue',
          // Pinia
          './stores': './stores',
        },
        shared: {
          vue: {
            // modulePreload: true,
            singleton: true,
          },
          pinia: {
            // modulePreload: true,
            singleton: true,
          },
        },
        manifest: true,
        /!* manifest: {
          fileName: '_nuxt/mf-manifest.json',
        }, *!/
      }), */
      // NOTE: If needs to support build targets lower than chrome89, you can use 'vite-plugin-top-level-await' plugin
      topLevelAwait({
        // The export name of top-level await promise for each chunk module
        promiseExportName: '__tla',
        // The function to generate import names of top-level await promise in each chunk module
        promiseImportName: (i) => `__tla_${i}`,
      }),
    ],
    // NOTE: If needs to support build targets lower than chrome89, you can use 'vite-plugin-top-level-await' plugin
    build: {
      // target: 'chrome89',
      target: 'es2022',
      sourcemap: true,
      /* commonjsOptions: {
        ignore(id: string) {
          return id.includes('@module-federation/runtime');
        },
      }, */
      /* rollupOptions: {
        // Mark these virtual modules as external for Rollup
        external: ['virtual:mf-REMOTE_ENTRY_ID'],
      }, */
      /* rollupOptions: {
        // Mark these virtual modules as external for Rollup
        external: ['__federation__', '__federation_fn_satisfy', 'virtual:__federation_fn_import'],
      }, */
      /* rollupOptions: {
        external: ['__federation_fn_satisfy'],
        /!* plugins: [
          // 파일 처리 과정을 로그로 남기는 커스텀 플러그인
          {
            name: 'file-processing-logger',
            buildStart(opts) {
              console.log('🚀 Rollup build started');
            },
            resolveId(id, importer) {
              console.log('🔍 Resolving: ', id, importer);
              if (id && !id.includes('node_modules')) {
                console.log(`🔍 Resolving: ${id} ${importer ? `(imported by ${importer})` : ''}`);
              }
              return null; // 기본 해결 로직 사용
            },
            load(id) {
              if (id && !id.includes('node_modules') && !id.includes('virtual:')) {
                console.log(`📁 Loading: ${id}`);
              }
              return null; // 기본 로드 로직 사용
            },
            transform(code, id) {
              if (id && !id.includes('node_modules') && !id.includes('virtual:')) {
                console.log(`🔄 Transforming: ${id} (${code.length} chars)`);
              }
              return null; // 기본 변환 로직 사용
            },
            generateBundle(opts, bundle) {
              console.log('📦 Generating bundle with chunks:');
              Object.keys(bundle).forEach((fileName) => {
                const chunk = bundle[fileName];
                if (!chunk) {
                  console.warn(`⚠️  Unknown bundle entry: ${fileName}`);
                  return;
                }
                if (chunk.type === 'chunk') {
                  console.log(`  📄 Chunk: ${fileName} (${chunk.modules ? Object.keys(chunk.modules).length : 0} modules)`);
                } else if (chunk.type === 'asset') {
                  console.log(`  📎 Asset: ${fileName}`);
                } else {
                  console.warn(`⚠️  Unknown type for: ${fileName}`);
                }
              });
            },
            writeBundle() {
              console.log('✅ Bundle written to disk');
            },
          },
        ], *!/
        /!* output: {
          manualChunks(id) {
            console.log('🧩 Manual chunk decision for:', id);
            /!* if (id.indexOf('/@module-federation/runtime') > -1) {
              return 'mfruntime';
            } *!/
          },
        }, *!/
        /!* plugins: [
          federation({
            name: '@nextssp/web/host',
            filename: 'remoteEntry.js',
            exposes: {
              './app': '~/app.vue',
            },
            shared: {
              vue: {},
              /!* pinia: {
                // modulePreload: true,
              }, *!/
            },
          }),
        ], *!/
      }, */
    },
    css: {
      devSourcemap: true,
      /* preprocessorOptions: {
        scss: {
        },
      }, */
    },
    server: {
      fs: {
        // Allow serving files from one level up to the project root
        allow: ['..'],
      },
    },
    vue: {
      customElement: true,
      // include: [/\.vue$/, /\.md$/],
      // exclude: ['**/node_modules/**'],
    },
    vueJsx: {
      mergeProps: true,
      // include: [/\.vue$/, /\.md$/],
      // exclude: ['**/node_modules/**'],
    },
  },

  debug: true,

  eslint: {
    config: {
      stylistic: {
        quotes: 'single', // Enforce single quotes
        // You can also add other stylistic rules here, e.g.,
        semi: true,
        indent: 2,
      },
    },
  },

  i18n: {
    langDir: 'locales',
    strategy: 'no_prefix',
    locales: [
      { code: 'ko', language: 'ko-KR', file: 'ko.json', name: '한국어' },
      { code: 'en', language: 'en-US', file: 'en.json', name: 'English' },
      { code: 'fr', language: 'fr-FR', file: 'fr.json', name: 'French' },
    ],
    defaultLocale: 'ko',
    experimental: {
      localeDetector: 'localeDetector.ts',
      preload: true,
    },
  },

  nuxtQuery: {
    autoImports: ['useQuery', 'useInfiniteQuery', 'useMutation', 'useQueryClient', 'useIsFetching', 'useIsMutating'],
    devtools: true,
    /**
     * These are the same options as the QueryClient
     * from @tanstack/vue-query, which will be passed
     * to the QueryClient constructor
     * More details: https://tanstack.com/query/v5/docs/reference/QueryClient
     */
    queryClientOptions: {
      defaultOptions: {
        queries: {
          // for example disable refetching on window focus
          refetchOnWindowFocus: false,

          // or change the default refetch interval
          refetchInterval: 5000,
        },
      },
    },
  },

  pinia: {
    storesDirs: ['./stores/**'],
  },
});
